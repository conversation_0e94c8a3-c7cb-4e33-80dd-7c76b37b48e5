<div class="user-details-modal">
  <!-- <PERSON><PERSON> -->
  <div class="modal-header">
    <h2 mat-dialog-title class="modal-title">User Details</h2>
    <button
      mat-icon-button
      class="close-button"
      (click)="onClose()"
      aria-label="Close dialog"
    >
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Modal Content -->
  <div mat-dialog-content class="modal-content">
    <!-- User Profile Section -->
    <div class="profile-section">
      <div class="profile-header">
        <!-- Profile Picture -->
        <div class="profile-picture">
          <img
            *ngIf="data.user.profileImageUrl"
            [src]="data.user.profileImageUrl"
            [alt]="data.user.fullName"
            class="profile-image"
          />
          <div *ngIf="!data.user.profileImageUrl" class="profile-placeholder">
            <span class="initials">{{ getUserInitials(data.user) }}</span>
          </div>
        </div>

        <!-- Basic Info -->
        <div class="basic-info">
          <h3 class="user-name">{{ data.user.fullName || 'N/A' }}</h3>
          <p class="user-email">{{ data.user.email || 'N/A' }}</p>
          <div class="status-badge">
            <mat-chip [class]="data.user.isActive ? 'status-active' : 'status-inactive'">
              <mat-icon>{{ data.user.isActive ? 'check_circle' : 'cancel' }}</mat-icon>
              {{ data.user.isActive ? 'Active' : 'Inactive' }}
            </mat-chip>
          </div>
        </div>
      </div>
    </div>

    <!-- Details Grid -->
    <div class="details-grid">
      <!-- Contact Information -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>contact_phone</mat-icon>
            Contact Information
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="info-row">
            <span class="info-label">Email:</span>
            <span class="info-value">{{ data.user.email || 'N/A' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Phone:</span>
            <span class="info-value">{{ data.user.phoneNumber || 'N/A' }}</span>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Role Information -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>security</mat-icon>
            Role & Permissions
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="roles-container">
            <mat-chip-set>
              <mat-chip
                *ngFor="let role of data.user.roles"
                [class]="getRoleClass(role)"
              >
                {{ getRoleDisplayName(role) }}
              </mat-chip>
            </mat-chip-set>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Account Information -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>account_circle</mat-icon>
            Account Information
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="info-row">
            <span class="info-label">User ID:</span>
            <span class="info-value">{{ data.user.id || 'N/A' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Created On:</span>
            <span class="info-value">{{ formatDate(data.user.createdOn) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Status:</span>
            <span class="info-value">{{ data.user.isActive ? 'Active' : 'Inactive' }}</span>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Additional Information -->
      <mat-card class="info-card" *ngIf="data.user.description">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>description</mat-icon>
            Description
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p class="description-text">{{ data.user.description }}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Modal Actions -->
  <div mat-dialog-actions class="modal-actions">
    <button mat-button (click)="onClose()" class="cancel-button">
      Close
    </button>
    <button mat-raised-button color="primary" class="edit-button">
      <mat-icon>edit</mat-icon>
      Edit User
    </button>
  </div>
</div>
