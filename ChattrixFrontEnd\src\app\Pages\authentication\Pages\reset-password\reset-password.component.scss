/* Reset Password Component Specific Styles */

.reset-card {
  max-width: 450px; // Slightly wider for password strength indicator
}

.full-width {
  width: 100%;
  margin-bottom: var(--spacing-md);
}

.reset-button {
  height: 44px;
  font-size: var(--font-size-base);
  font-weight: 500;
  text-transform: none;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  position: relative;

  &:disabled {
    opacity: 0.6;
  }

  .button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    position: relative;
  }

  .hidden {
    visibility: hidden;
    opacity: 0;
  }
}

.button-spinner {
  position: absolute;

  transform: translate(-50%, -50%);
}

/* Password Strength Indicator */
.password-strength {
  margin-top: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);

  h4 {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
  }
}

.strength-requirement {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);

  &:last-child {
    margin-bottom: 0;
  }

  .requirement-icon {
    width: 18px;
    height: 18px;
    margin-right: var(--spacing-sm);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    transition: all var(--transition-normal);

    &.met {
      background-color: var(--success);
      color: white;
      transform: scale(1);
    }

    &.unmet {
      background-color: var(--text-disabled);
      color: white;
      transform: scale(0.9);
      opacity: 0.7;
    }
  }

  .requirement-text {
    font-size: var(--font-size-xs);
    transition: color var(--transition-normal);

    &.met {
      color: var(--success);
      font-weight: 500;
    }

    &.unmet {
      color: var(--text-disabled);
    }
  }
}

/* Back to Login */
.back-to-login {
  text-align: center;
  padding-top: var(--spacing-lg);
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--accent-green) !important;
  text-transform: none;
  font-size: var(--font-size-sm);
  margin: 0 auto;

  &:hover {
    color: var(--accent-green-hover) !important;
    background: transparent;
  }
}

/* Logo Fallback Styles */
.logo-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--accent-green) 0%,
    var(--accent-green-light) 100%
  );
  border-radius: 50%;
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Material UI Form Field Customizations */
::ng-deep .reset-card .mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    background-color: var(--bg-input);
    border-radius: var(--radius-md);
  }

  .mat-mdc-form-field-label {
    color: var(--text-secondary);
  }

  .mat-mdc-form-field-input {
    color: var(--text-primary);
  }

  .mat-mdc-form-field-icon-suffix {
    color: var(--text-secondary);
  }

  &.mat-focused {
    .mat-mdc-form-field-label {
      color: var(--accent-green);
    }
  }

  // Compact form field wrapper
  .mat-mdc-form-field-wrapper {
    padding-bottom: 0;
  }

  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
    min-height: 16px;
    font-size: var(--font-size-xs);
  }
}

/* Snackbar Styles */
::ng-deep .error-snackbar {
  background-color: var(--error) !important;
  color: var(--text-primary) !important;
}

::ng-deep .success-snackbar {
  background-color: var(--success) !important;
  color: var(--text-primary) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .reset-card {
    .auth-header {
      padding: var(--spacing-sm) var(--spacing-md) var(--spacing-xs);
    }

    .auth-form {
      padding: var(--spacing-sm) var(--spacing-md);
    }

    .auth-footer {
      padding: var(--spacing-sm) var(--spacing-md);
    }

    .full-width {
      margin-bottom: var(--spacing-sm);
    }

    .password-strength {
      margin-bottom: var(--spacing-md);
      padding: var(--spacing-sm);
    }
  }
}

@media (max-width: 480px) {
  .reset-card {
    margin: var(--spacing-xs);
    max-width: calc(100vw - 1rem);

    .auth-header {
      padding: var(--spacing-xs) var(--spacing-sm) var(--spacing-xs);
    }

    .auth-form {
      padding: var(--spacing-xs) var(--spacing-sm);
    }

    .auth-footer {
      padding: var(--spacing-xs) var(--spacing-sm);
    }

    .auth-logo {
      width: 45px;
      height: 45px;
      margin-bottom: var(--spacing-xs);
    }

    .auth-title {
      font-size: var(--font-size-lg);
      margin-bottom: 4px;
    }

    .auth-subtitle {
      font-size: var(--font-size-xs);
      margin-bottom: 0;
    }

    .full-width {
      margin-bottom: 8px;
    }

    .reset-button {
      height: 40px;
      margin-bottom: 8px;
    }

    .password-strength {
      margin-top: 8px;
      margin-bottom: var(--spacing-sm);
      padding: var(--spacing-xs);

      h4 {
        font-size: 11px;
        margin-bottom: 6px;
      }
    }

    .strength-requirement {
      margin-bottom: 4px;

      .requirement-icon {
        width: 14px;
        height: 14px;
        font-size: 10px;
        margin-right: 6px;
      }

      .requirement-text {
        font-size: 10px;
      }
    }
  }
}

/* Focus and Hover States */
.mat-mdc-button:focus {
  outline: 2px solid var(--accent-green);
  outline-offset: 2px;
}

.mat-mdc-form-field:focus-within {
  .mat-mdc-text-field-wrapper {
    border: 2px solid var(--accent-green);
  }
}

/* Loading State Animation */
.button-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Form validation visual feedback */
.mat-mdc-form-field.ng-invalid.ng-touched {
  .mat-mdc-text-field-wrapper {
    border: 1px solid var(--error);
  }
}

.mat-mdc-form-field.ng-valid.ng-touched {
  .mat-mdc-text-field-wrapper {
    border: 1px solid var(--success);
  }
}

/* Password strength animation */
.strength-requirement {
  animation: fadeInUp 0.3s ease-in-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success animation for met requirements */
.requirement-icon.met {
  animation: checkmark 0.5s ease-in-out;
}

@keyframes checkmark {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
