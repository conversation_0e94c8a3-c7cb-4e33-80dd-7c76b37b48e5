/* OTP Verification Component Specific Styles */

.otp-card {
  max-width: 420px;
}

.full-width {
  width: 100%;
}

/* OTP Input Container */
.otp-container {
  margin-bottom: var(--spacing-xl);
}

.otp-input-field {
  width: 100%;

  .mat-mdc-text-field-wrapper {
    background-color: var(--bg-input);
    border-radius: var(--radius-md);
  }

  .mat-mdc-form-field-input {
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    font-weight: 600;
    text-align: center;
    letter-spacing: 0.5em;
    padding: var(--spacing-md);
  }

  .mat-mdc-form-field-label {
    color: var(--text-secondary);
  }

  &.mat-focused {
    .mat-mdc-form-field-label {
      color: var(--accent-green);
    }

    .mat-mdc-text-field-wrapper {
      border-color: var(--accent-green);
      box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }
  }

  &.mat-form-field-invalid {
    .mat-mdc-text-field-wrapper {
      border-color: var(--error);
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
  }
}

.otp-input-single {
  text-align: center;
  letter-spacing: 0.3em;
  font-size: var(--font-size-xl);
  font-weight: 600;
}

/* Verify Button */
.verify-button {
  height: 48px;
  font-size: var(--font-size-base);
  font-weight: 500;
  text-transform: none;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-xl);
  position: relative;

  &:disabled {
    opacity: 0.6;
  }

  .button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    position: relative;
  }

  .hidden {
    visibility: hidden;
    opacity: 0;
  }
}

.button-spinner {
  position: absolute;

  transform: translate(-50%, -50%);
}

/* Resend Section */
.resend-section {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.resend-text {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-md);
}

.resend-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  text-transform: none;
  margin: 0 auto;
  position: relative;

  &:not(.disabled) {
    color: var(--accent-green) !important;

    &:hover {
      color: var(--accent-green-hover) !important;
      background: transparent;
    }
  }

  &.disabled {
    color: var(--text-disabled) !important;
    cursor: not-allowed;
  }

  .button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    position: relative;
  }

  .hidden {
    visibility: hidden;
    opacity: 0;
  }
}

.resend-spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* Back to Login */
.back-to-login {
  text-align: center;
  padding-top: var(--spacing-lg);
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--accent-green) !important;
  text-transform: none;
  font-size: var(--font-size-sm);
  margin: 0 auto;

  &:hover {
    color: var(--accent-green-hover) !important;
    background: transparent;
  }
}

/* Snackbar Styles */
::ng-deep .error-snackbar {
  background-color: var(--error) !important;
  color: var(--text-primary) !important;
}

::ng-deep .success-snackbar {
  background-color: var(--success) !important;
  color: var(--text-primary) !important;
}

/* Responsive Design */
@media (max-width: 480px) {
  .otp-card {
    margin: var(--spacing-sm);
    max-width: calc(100vw - 2rem);
  }

  .auth-header {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .auth-form {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .auth-footer {
    padding: var(--spacing-md);
  }

  .auth-logo {
    width: 60px;
    height: 60px;
  }

  .auth-title {
    font-size: var(--font-size-xl);
  }

  .otp-input-field {
    .mat-mdc-form-field-input {
      font-size: var(--font-size-lg);
      letter-spacing: 0.2em;
    }
  }
}

@media (max-width: 360px) {
  .otp-input-field {
    .mat-mdc-form-field-input {
      font-size: var(--font-size-base);
      letter-spacing: 0.1em;
    }
  }
}

/* Focus and Hover States */
.mat-mdc-button:focus {
  outline: 2px solid var(--accent-green);
  outline-offset: 2px;
}

/* Loading State Animation */
.button-spinner,
.resend-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* OTP Input Animation */
.otp-input-field {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success State Animation */
.otp-input-field.mat-form-field-valid {
  .mat-mdc-text-field-wrapper {
    animation: successPulse 0.6s ease-in-out;
    border-color: var(--success);
    background-color: rgba(16, 185, 129, 0.1);
  }
}

@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* Timer Display Styling */
.resend-button .mat-icon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Logo Fallback Styles */
.logo-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--accent-green) 0%,
    var(--accent-green-light) 100%
  );
  border-radius: 50%;
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
