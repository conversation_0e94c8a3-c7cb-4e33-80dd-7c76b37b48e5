﻿using AutoMapper;
using ChattrixBackend.Core.Entities.UserManagement.RegisterModel;
using ChattrixBackend.Core.Entities.UserManagement.UserDetailsModel;
using ChattrixBackend.Core.Entities.UserManagement.UserModel;

namespace Server.Core.MappingProfile {
    public class UserManagementMappingProfile : Profile {

        public UserManagementMappingProfile() {
            // Map Register to ApplicationUser
            CreateMap<AddUser, ApplicationUser>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(_ => DateTime.UtcNow))
                .ForMember(dest => dest.ProfilePictureUrl, opt => opt.MapFrom(src => src.ProfileImageUrl));

            CreateMap<UserDetails, ApplicationUser>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(_ => DateTime.UtcNow));
        }
    }
}
