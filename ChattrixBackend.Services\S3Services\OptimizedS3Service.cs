using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System.Drawing;
using System.Drawing.Imaging;

namespace ChattrixBackend.Services.S3Services {
    public class OptimizedS3Service : IS3Service {
        private readonly IAmazonS3 _s3Client;
        private readonly string _bucketName = string.Empty;
        private readonly IConfiguration _configuration;
        private readonly TransferUtility _fileTransferUtility;

        public OptimizedS3Service(IAmazonS3 s3Client, IConfiguration configuration) {
            _s3Client = s3Client;
            _configuration = configuration;
            _bucketName = _configuration["AWS:S3:BucketName"] ?? throw new ArgumentNullException("AWS:S3:BucketName configuration is missing");
            _fileTransferUtility = new TransferUtility(_s3Client);
        }

        public async Task<string?> UploadFileAsync(IFormFile? file, string? folderName) {
            try {
                if (file == null || file.Length == 0)
                    return null;

                // Generate a unique file name
                var fileName = $"{Guid.NewGuid():N}_{Path.GetFileName(file.FileName)}";

                // Create the full key (path) in S3
                var key = string.IsNullOrEmpty(folderName)
                    ? fileName
                    : $"{folderName}/{fileName}";

                // Check if the file is an image that can be compressed
                if (IsImage(file.ContentType)) {
                    try {
                        // Compress the image before uploading
                        using var compressedStream = new MemoryStream();
                        await CompressImageAsync(file, compressedStream, 80); // 80% quality
                        compressedStream.Position = 0;

                        var uploadRequest = new TransferUtilityUploadRequest {
                            InputStream = compressedStream,
                            Key = key,
                            BucketName = _bucketName,
                            ContentType = file.ContentType
                        };

                        await _fileTransferUtility.UploadAsync(uploadRequest);
                    }
                    catch (Exception ex) {
                        // If compression fails, fall back to direct upload
                        using var memoryStream = new MemoryStream();
                        await file.CopyToAsync(memoryStream);
                        memoryStream.Position = 0;

                        var uploadRequest = new TransferUtilityUploadRequest {
                            InputStream = memoryStream,
                            Key = key,
                            BucketName = _bucketName,
                            ContentType = file.ContentType
                        };

                        await _fileTransferUtility.UploadAsync(uploadRequest);
                    }
                }
                else {
                    // For non-image files, upload directly
                    using var memoryStream = new MemoryStream();
                    await file.CopyToAsync(memoryStream);
                    memoryStream.Position = 0;

                    var uploadRequest = new TransferUtilityUploadRequest {
                        InputStream = memoryStream,
                        Key = key,
                        BucketName = _bucketName,
                        ContentType = file.ContentType
                    };

                    await _fileTransferUtility.UploadAsync(uploadRequest);
                }

                // Return the key (path) of the file in S3
                return key;
            }
            catch (Exception ex) {
                Console.WriteLine($"Error uploading file to S3: {ex.Message}");
                throw;
            }
        }

        // Optimized method to upload multiple files in parallel
        public async Task<Dictionary<string, string>> UploadMultipleFilesAsync(Dictionary<string, IFormFile> files) {
            var results = new Dictionary<string, string>();
            var uploadTasks = new List<Task<KeyValuePair<string, string>>>();

            foreach (var file in files) {
                uploadTasks.Add(UploadSingleFileAsync(file.Key, file.Value));
            }

            var completedTasks = await Task.WhenAll(uploadTasks);

            foreach (var result in completedTasks) {
                results.Add(result.Key, result.Value);
            }

            return results;
        }

        private async Task<KeyValuePair<string, string>> UploadSingleFileAsync(string fileKey, IFormFile file) {
            string folderName = DetermineFolderName(fileKey);
            string? uploadedKey = await UploadFileAsync(file, folderName);
            return new KeyValuePair<string, string>(fileKey, uploadedKey ?? string.Empty);
        }

        private static string DetermineFolderName(string fileKey) => fileKey switch {
            "ResourceImage" => "ResourceImages",
            "ResourceLogo" => "ResourceLogos",
            "EventImage" => "EventImages",
            _ => "Misc"
        };

        private static bool IsImage(string? contentType) {
            return contentType != null && contentType.ToLower().StartsWith("image/", StringComparison.OrdinalIgnoreCase);
        }

        private async Task CompressImageAsync(IFormFile imageFile, Stream outputStream, int quality) {
            using var stream = imageFile.OpenReadStream();
            using var image = Image.FromStream(stream);

            var encoderParameters = new EncoderParameters(1);
            encoderParameters.Param[0] = new EncoderParameter(Encoder.Quality, quality);

            var codec = GetEncoderInfo(GetMimeType(imageFile.ContentType));
            if (codec != null) {
                image.Save(outputStream, codec, encoderParameters);
            }
            else {
                // If we can't find a codec, save as JPEG
                image.Save(outputStream, ImageFormat.Jpeg);
            }
        }

        private static ImageCodecInfo? GetEncoderInfo(string mimeType) {
            var codecs = ImageCodecInfo.GetImageEncoders();
            return codecs.FirstOrDefault(codec => codec.MimeType == mimeType);
        }

        private static string GetMimeType(string contentType) => contentType.ToLower() switch {
            "image/jpeg" or "image/jpg" => "image/jpeg",
            "image/png" => "image/png",
            "image/gif" => "image/gif",
            _ => "image/jpeg" // Default to JPEG
        };

        // Implement the rest of the IS3Service interface
        public async Task<bool> DeleteFileAsync(string? fileUrl) {
            // Implementation remains the same
            try {
                if (string.IsNullOrEmpty(fileUrl))
                    return false;

                var key = GetKeyFromUrl(fileUrl);
                if (string.IsNullOrEmpty(key))
                    return false;

                var deleteRequest = new DeleteObjectRequest {
                    BucketName = _bucketName,
                    Key = key
                };

                var response = await _s3Client.DeleteObjectAsync(deleteRequest);
                return response.HttpStatusCode == System.Net.HttpStatusCode.NoContent;
            }
            catch (Exception ex) {
                Console.WriteLine($"Error deleting file from S3: {ex.Message}");
                return false;
            }
        }

        public string? GetFileUrl(string? key) {
            if (string.IsNullOrEmpty(key))
                return null;

            var region = _configuration["AWS:Region"] ?? "eu-north-1"; // Default to Stockholm region
            return $"https://{_bucketName}.s3.{region}.amazonaws.com/{key}";
        }

        private static string? GetKeyFromUrl(string? fileUrl) {
            if (string.IsNullOrEmpty(fileUrl))
                return null;

            if (!fileUrl.StartsWith("http", StringComparison.OrdinalIgnoreCase))
                return fileUrl;

            var uri = new Uri(fileUrl);
            var key = uri.AbsolutePath;

            if (key.StartsWith('/'))
                key = key[1..]; // Use range operator instead of Substring

            return key;
        }
    }
}
