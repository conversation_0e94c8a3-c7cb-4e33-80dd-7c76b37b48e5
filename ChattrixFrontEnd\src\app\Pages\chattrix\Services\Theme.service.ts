import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export type ThemeMode = 'light' | 'dark';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_STORAGE_KEY = 'chattrix-theme';
  private readonly DEFAULT_THEME: ThemeMode = 'dark'; // Default to dark theme as per current design
  
  private _currentTheme$ = new BehaviorSubject<ThemeMode>(this.DEFAULT_THEME);
  public readonly currentTheme$ = this._currentTheme$.asObservable();

  constructor() {
    this.initializeTheme();
  }

  /**
   * Initialize theme from localStorage or system preference
   */
  private initializeTheme(): void {
    const savedTheme = this.getSavedTheme();
    const systemTheme = this.getSystemTheme();
    
    // Priority: saved theme > system preference > default
    const initialTheme = savedTheme || systemTheme || this.DEFAULT_THEME;
    
    this.setTheme(initialTheme, false); // Don't save to storage on init
  }

  /**
   * Get saved theme from localStorage
   */
  private getSavedTheme(): ThemeMode | null {
    try {
      const saved = localStorage.getItem(this.THEME_STORAGE_KEY);
      return saved === 'light' || saved === 'dark' ? saved : null;
    } catch {
      return null;
    }
  }

  /**
   * Get system theme preference
   */
  private getSystemTheme(): ThemeMode {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: light)').matches ? 'light' : 'dark';
    }
    return this.DEFAULT_THEME;
  }

  /**
   * Get current theme
   */
  get currentTheme(): ThemeMode {
    return this._currentTheme$.value;
  }

  /**
   * Check if current theme is dark
   */
  get isDarkMode(): boolean {
    return this.currentTheme === 'dark';
  }

  /**
   * Check if current theme is light
   */
  get isLightMode(): boolean {
    return this.currentTheme === 'light';
  }

  /**
   * Set theme and optionally save to localStorage
   */
  setTheme(theme: ThemeMode, saveToStorage: boolean = true): void {
    this._currentTheme$.next(theme);
    this.applyThemeToDocument(theme);
    
    if (saveToStorage) {
      this.saveTheme(theme);
    }
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme(): void {
    const newTheme: ThemeMode = this.isDarkMode ? 'light' : 'dark';
    this.setTheme(newTheme);
  }

  /**
   * Apply theme classes to document
   */
  private applyThemeToDocument(theme: ThemeMode): void {
    if (typeof document !== 'undefined') {
      const body = document.body;
      
      // Remove existing theme classes
      body.classList.remove('light-theme', 'dark-theme');
      
      // Add new theme class
      body.classList.add(`${theme}-theme`);
      
      // Update CSS custom properties for Material UI
      this.updateCSSVariables(theme);
    }
  }

  /**
   * Update CSS custom properties based on theme
   */
  private updateCSSVariables(theme: ThemeMode): void {
    if (typeof document !== 'undefined') {
      const root = document.documentElement;
      
      if (theme === 'light') {
        // Light theme variables
        root.style.setProperty('--primary-dark', '#ffffff');
        root.style.setProperty('--primary-darker', '#f8f9fa');
        root.style.setProperty('--secondary-dark', '#ffffff');
        root.style.setProperty('--text-primary', '#000000');
        root.style.setProperty('--text-secondary', '#6c757d');
        root.style.setProperty('--text-muted', '#adb5bd');
        root.style.setProperty('--bg-primary', '#ffffff');
        root.style.setProperty('--bg-secondary', '#f8f9fa');
        root.style.setProperty('--bg-tertiary', '#e9ecef');
        root.style.setProperty('--bg-card', '#ffffff');
        root.style.setProperty('--border-primary', '#dee2e6');
        root.style.setProperty('--border-secondary', '#adb5bd');
        
        // Material UI theme variables for light mode
        root.style.setProperty('--mdc-theme-surface', '#ffffff');
        root.style.setProperty('--mdc-theme-background', '#fafafa');
        root.style.setProperty('--mdc-theme-on-surface', '#000000');
        root.style.setProperty('--mdc-theme-on-background', '#000000');
      } else {
        // Dark theme variables (restore original)
        root.style.setProperty('--primary-dark', '#0f172a');
        root.style.setProperty('--primary-darker', '#020617');
        root.style.setProperty('--secondary-dark', '#1e293b');
        root.style.setProperty('--text-primary', '#ffffff');
        root.style.setProperty('--text-secondary', '#94a3b8');
        root.style.setProperty('--text-muted', '#64748b');
        root.style.setProperty('--bg-primary', '#0f172a');
        root.style.setProperty('--bg-secondary', '#1e293b');
        root.style.setProperty('--bg-tertiary', '#334155');
        root.style.setProperty('--bg-card', '#1e293b');
        root.style.setProperty('--border-primary', '#334155');
        root.style.setProperty('--border-secondary', '#475569');
        
        // Material UI theme variables for dark mode
        root.style.setProperty('--mdc-theme-surface', '#1e293b');
        root.style.setProperty('--mdc-theme-background', '#0f172a');
        root.style.setProperty('--mdc-theme-on-surface', '#ffffff');
        root.style.setProperty('--mdc-theme-on-background', '#ffffff');
      }
    }
  }

  /**
   * Save theme to localStorage
   */
  private saveTheme(theme: ThemeMode): void {
    try {
      localStorage.setItem(this.THEME_STORAGE_KEY, theme);
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error);
    }
  }

  /**
   * Listen to system theme changes
   */
  listenToSystemThemeChanges(): void {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: light)');
      
      mediaQuery.addEventListener('change', (e) => {
        // Only update if no theme is saved (user hasn't manually set a preference)
        if (!this.getSavedTheme()) {
          const systemTheme: ThemeMode = e.matches ? 'light' : 'dark';
          this.setTheme(systemTheme, false);
        }
      });
    }
  }

  /**
   * Reset theme to system preference
   */
  resetToSystemTheme(): void {
    try {
      localStorage.removeItem(this.THEME_STORAGE_KEY);
    } catch {
      // Ignore localStorage errors
    }
    
    const systemTheme = this.getSystemTheme();
    this.setTheme(systemTheme, false);
  }
}
