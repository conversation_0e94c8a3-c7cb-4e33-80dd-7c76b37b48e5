# Chattrix Chat Backend Implementation Guide

## Overview
This document provides a comprehensive guide for the WebSocket-based chat system implementation in the Chattrix backend. The system supports real-time messaging, group chats, message persistence, user presence tracking, and advanced features like typing indicators and read receipts.

## Architecture Overview

### Core Components
1. **WebSocket Connection Management** - Handles real-time connections without SignalR
2. **Chat Services** - Business logic for messaging and conversation management
3. **Message Routing** - Routes messages to appropriate recipients
4. **Security Services** - Authentication, authorization, and rate limiting
5. **Database Layer** - Entity Framework Core with optimized schema

### Key Features Implemented
- ✅ Real-time messaging using WebSockets
- ✅ One-to-one private messaging
- ✅ Group chat functionality
- ✅ Message persistence and history
- ✅ JWT authentication integration
- ✅ Online/offline status tracking
- ✅ Message delivery status (sent, delivered, read)
- ✅ Typing indicators
- ✅ Message read receipts
- ✅ Rate limiting and security
- ✅ File upload support
- ✅ Message search functionality

## Database Schema

### Core Entities
- **Conversations** - Chat rooms (private/group)
- **ConversationParticipants** - User membership in conversations
- **Messages** - Chat messages with metadata
- **MessageStatus** - Delivery and read status tracking
- **UserPresence** - User online/offline status history
- **TypingIndicators** - Real-time typing status

## API Endpoints

### Chat Controller (`/api/chat`)
- `POST /conversations/{conversationId}/messages` - Send message
- `GET /conversations/{conversationId}/messages` - Get message history
- `PUT /messages/{messageId}` - Edit message
- `DELETE /messages/{messageId}` - Delete message
- `POST /conversations/private` - Create private conversation
- `POST /conversations/group` - Create group conversation
- `GET /conversations` - Get user conversations
- `GET /conversations/{conversationId}` - Get conversation details

### Presence Controller (`/api/presence`)
- `POST /status` - Update user presence
- `GET /status/{userId}` - Get user presence
- `GET /online` - Get online users

## WebSocket Protocol

### Connection
Connect to: `ws://localhost:5005/ws`

### Message Types
1. **Authentication**
```json
{
  "type": "authenticate",
  "data": {
    "token": "jwt_token_here",
    "deviceInfo": "optional_device_info"
  }
}
```

2. **Send Message**
```json
{
  "type": "message",
  "data": {
    "conversationId": "conversation_id",
    "content": "message_content",
    "type": "text",
    "replyToMessageId": "optional_reply_id",
    "clientMessageId": "optional_client_id"
  }
}
```

3. **Typing Indicator**
```json
{
  "type": "typing",
  "data": {
    "conversationId": "conversation_id",
    "isTyping": true
  }
}
```

4. **Presence Update**
```json
{
  "type": "presence",
  "data": {
    "status": "online"
  }
}
```

5. **Message Status Update**
```json
{
  "type": "message_status",
  "data": {
    "messageId": "message_id",
    "status": "read"
  }
}
```

### Server Events
- `message_received` - New message in conversation
- `user_typing` - User typing status change
- `user_presence_updated` - User online status change
- `message_status_updated` - Message delivery/read status
- `conversation_updated` - Conversation metadata change
- `participant_updated` - Participant added/removed

## Setup Instructions

### 1. Database Migration
```powershell
# Run the migration script
.\Scripts\CreateChatMigration.ps1
```

### 2. Configuration
Ensure your `appsettings.json` includes:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "your_connection_string"
  },
  "JWTSetting": {
    "securityKey": "your_jwt_secret",
    "ValidAudience": "http://localhost:4200",
    "ValidIssuer": "https://localhost:5020"
  }
}
```

### 3. Running the Application
```bash
dotnet run --project ChattrixBackend
```

## Security Features

### Authentication
- JWT token validation for WebSocket connections
- Role-based authorization for chat operations
- Message access control based on conversation membership

### Rate Limiting
- Message sending: 30 requests/minute
- Conversation creation: 5 requests/minute
- File uploads: 10 requests/minute
- Typing indicators: 60 requests/minute

### Authorization Checks
- Conversation access verification
- Message edit/delete permissions
- Participant management authorization

## Performance Optimizations

### Database
- Optimized indexes on frequently queried fields
- Efficient relationship configurations
- Pagination for message history

### WebSocket Management
- Connection pooling and cleanup
- Automatic inactive connection removal
- Efficient message routing

### Caching
- Memory cache for rate limiting
- Connection state management

## Testing the Implementation

### WebSocket Testing
Use a WebSocket client to test real-time functionality:
1. Connect to `ws://localhost:5005/ws`
2. Authenticate with JWT token
3. Send messages and observe real-time delivery

### API Testing
Use the provided HTTP endpoints to test:
1. Create conversations
2. Send messages via REST API
3. Retrieve message history
4. Update user presence

## Troubleshooting

### Common Issues
1. **WebSocket Connection Fails**
   - Verify JWT token is valid
   - Check CORS configuration
   - Ensure WebSocket middleware is properly configured

2. **Messages Not Delivering**
   - Check user is participant in conversation
   - Verify WebSocket connection is active
   - Check rate limiting status

3. **Database Errors**
   - Ensure migration is applied
   - Check connection string
   - Verify Entity Framework configuration

## Next Steps

### Potential Enhancements
1. Message reactions and emoji support
2. Message threading for replies
3. Voice/video call integration
4. Message scheduling
5. Advanced search with filters
6. Push notifications
7. Message encryption
8. File sharing with preview
9. Chat analytics and insights
10. Multi-device synchronization

This implementation provides a solid foundation for a production-ready chat system with room for future enhancements based on user requirements.
