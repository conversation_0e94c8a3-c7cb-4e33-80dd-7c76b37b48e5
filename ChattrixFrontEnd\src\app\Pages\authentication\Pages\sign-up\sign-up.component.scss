/* Signup Component Specific Styles */

.signup-card {
  max-width: 480px; // Slightly wider for signup form

  // Specific optimizations for signup form
  .auth-header {
    padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-xs);
  }

  .auth-form {
    padding: var(--spacing-sm) var(--spacing-lg);
  }

  .auth-footer {
    padding: var(--spacing-sm) var(--spacing-lg);
  }

  .auth-logo {
    width: 50px;
    height: 50px;
    margin-bottom: var(--spacing-sm);
  }

  .auth-title {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
  }

  .auth-subtitle {
    font-size: var(--font-size-xs);
    margin-bottom: 0;
  }
}

.full-width {
  width: 100%;
  margin-bottom: var(--spacing-sm);
}

.signup-button {
  height: 44px;
  font-size: var(--font-size-base);
  font-weight: 500;
  text-transform: none;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  position: relative;

  &:disabled {
    opacity: 0.6;
  }

  .button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    position: relative;
  }

  .hidden {
    visibility: hidden;
    opacity: 0;
  }
}

.button-spinner {
  position: absolute;

  transform: translate(-50%, -50%);
}

.login-prompt {
  margin: var(--spacing-sm) 0 0;
  text-align: center;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.login-link {
  color: var(--accent-green) !important;
  text-transform: none;
  font-weight: 500;
  padding: 0;
  min-width: auto;
  margin-left: var(--spacing-xs);

  &:hover {
    color: var(--accent-green-hover) !important;
    background: transparent;
  }
}

/* Password Strength Indicator */
.password-strength {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
}

.strength-requirement {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);

  &:last-child {
    margin-bottom: 0;
  }

  .requirement-icon {
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-xs);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;

    &.met {
      background-color: var(--success);
      color: white;
    }

    &.unmet {
      background-color: var(--text-disabled);
      color: white;
    }
  }

  .requirement-text {
    &.met {
      color: var(--success);
    }

    &.unmet {
      color: var(--text-disabled);
    }
  }
}

/* Material UI Form Field Customizations for Signup */
::ng-deep .signup-card .mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    background-color: var(--bg-input);
    border-radius: var(--radius-md);
  }

  .mat-mdc-form-field-label {
    color: var(--text-secondary);
  }

  .mat-mdc-form-field-input {
    color: var(--text-primary);
  }

  .mat-mdc-form-field-icon-suffix {
    color: var(--text-secondary);
  }

  &.mat-focused {
    .mat-mdc-form-field-label {
      color: var(--accent-green);
    }
  }

  // Compact form field wrapper for signup
  .mat-mdc-form-field-wrapper {
    padding-bottom: 0;
  }

  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 2px;
    min-height: 14px;
    font-size: var(--font-size-xs);
  }

  // Textarea specific styles
  textarea.mat-mdc-input-element {
    resize: vertical;
    min-height: 50px;
  }
}

/* Character counter styling */
::ng-deep .mat-mdc-form-field-hint {
  color: var(--text-muted);
  font-size: var(--font-size-xs);
}

/* Snackbar Styles */
::ng-deep .error-snackbar {
  background-color: var(--error) !important;
  color: var(--text-primary) !important;
}

::ng-deep .success-snackbar {
  background-color: var(--success) !important;
  color: var(--text-primary) !important;
}

/* Signup specific container optimization */
.signup-card .auth-container {
  min-height: 100vh;
  min-height: 100dvh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs);
  box-sizing: border-box;
}
/* Responsive Design for Signup */
@media (max-width: 768px) {
  .signup-card {
    .auth-header {
      padding: var(--spacing-xs) var(--spacing-md) var(--spacing-xs);
    }

    .auth-form {
      padding: var(--spacing-xs) var(--spacing-md);
    }

    .auth-footer {
      padding: var(--spacing-xs) var(--spacing-md);
    }

    .full-width {
      margin-bottom: var(--spacing-xs);
    }
  }
}

@media (max-width: 480px) {
  .signup-card {
    margin: var(--spacing-xs);
    max-width: calc(100vw - 1rem);

    .auth-header {
      padding: var(--spacing-xs) var(--spacing-sm) var(--spacing-xs);
    }

    .auth-form {
      padding: var(--spacing-xs) var(--spacing-sm);
    }

    .auth-footer {
      padding: var(--spacing-xs) var(--spacing-sm);
    }

    .auth-logo {
      width: 40px;
      height: 40px;
      margin-bottom: var(--spacing-xs);
    }

    .auth-title {
      font-size: var(--font-size-base);
      margin-bottom: 2px;
    }

    .auth-subtitle {
      font-size: 11px;
      margin-bottom: 0;
    }

    .full-width {
      margin-bottom: 6px;
    }

    .signup-button {
      height: 40px;
      margin-bottom: 6px;
    }

    .login-prompt {
      margin: 6px 0 0;
      font-size: 11px;
    }
  }
}

/* Focus and Hover States */
.mat-mdc-button:focus {
  outline: 2px solid var(--accent-green);
  outline-offset: 2px;
}

.mat-mdc-form-field:focus-within {
  .mat-mdc-text-field-wrapper {
    border: 2px solid var(--accent-green);
  }
}

/* Loading State Animation */
.button-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Form validation visual feedback */
.mat-mdc-form-field.ng-invalid.ng-touched {
  .mat-mdc-text-field-wrapper {
    border: 1px solid var(--error);
  }
}

.mat-mdc-form-field.ng-valid.ng-touched {
  .mat-mdc-text-field-wrapper {
    border: 1px solid var(--success);
  }
}

/* Logo Fallback Styles */
.logo-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--accent-green) 0%,
    var(--accent-green-light) 100%
  );
  border-radius: 50%;
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
