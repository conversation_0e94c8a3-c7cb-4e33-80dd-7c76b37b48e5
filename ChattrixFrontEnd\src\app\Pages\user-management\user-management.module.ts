import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

// Material UI Modules
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';

// Layout Module
import { LayoutModule } from '../../Layout/layout.module';

// Routing
import { UserManagementRoutingModule } from './user-management-routing.module';

// Components
import { UserListComponent } from './Pages/user-list/user-list.component';
import { UserDetailsModalComponent } from './Pages/user-details-modal/user-details-modal.component';
import { DeleteUserDialogComponent } from './Components/delete-user-dialog/delete-user-dialog.component';

@NgModule({
  declarations: [
    UserListComponent,
    UserDetailsModalComponent,
    DeleteUserDialogComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    UserManagementRoutingModule,
    LayoutModule,
    // Material UI Modules
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatTooltipModule,
  ],
})
export class UserManagementModule {}
