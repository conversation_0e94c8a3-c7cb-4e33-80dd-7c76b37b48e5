import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { UserDetails } from '../../Models/UserManagement';

@Component({
  selector: 'app-user-details-modal',
  standalone: false,
  templateUrl: './user-details-modal.component.html',
  styleUrl: './user-details-modal.component.scss'
})
export class UserDetailsModalComponent {
  constructor(
    public dialogRef: MatDialogRef<UserDetailsModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { user: UserDetails }
  ) {}

  onClose(): void {
    this.dialogRef.close();
  }

  getUserInitials(user: UserDetails): string {
    if (user.fullName) {
      const names = user.fullName.split(' ');
      if (names.length >= 2) {
        return names[0].charAt(0) + names[1].charAt(0);
      }
      return names[0].charAt(0);
    }
    return user.email?.charAt(0).toUpperCase() || 'U';
  }

  formatDate(date: Date): string {
    if (!date) return 'N/A';
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getRoleDisplayName(role: string): string {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'Administrator';
      case 'super admin':
      case 'superadmin':
        return 'Super Administrator';
      case 'user':
        return 'User';
      default:
        return role;
    }
  }

  getRoleClass(role: string): string {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'role-admin';
      case 'super admin':
      case 'superadmin':
        return 'role-super-admin';
      case 'user':
        return 'role-user';
      default:
        return 'role-default';
    }
  }
}
