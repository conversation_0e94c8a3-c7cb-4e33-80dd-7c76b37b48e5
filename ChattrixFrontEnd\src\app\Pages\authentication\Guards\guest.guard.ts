import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable, map, take } from 'rxjs';

import { AuthenticationService } from '../Services/Authentication.service';
import { AuthStateService } from '../Services/AuthState.service';

@Injectable({
  providedIn: 'root'
})
export class GuestGuard implements CanActivate {

  constructor(
    private authService: AuthenticationService,
    private authState: AuthStateService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    return this.authState.authState$.pipe(
      take(1),
      map(authState => {
        const isAuthenticated = authState.isAuthenticated;
        
        if (!isAuthenticated) {
          return true;
        } else {
          // Redirect authenticated users to dashboard
          this.router.navigate(['/dashboard']);
          return false;
        }
      })
    );
  }
}
