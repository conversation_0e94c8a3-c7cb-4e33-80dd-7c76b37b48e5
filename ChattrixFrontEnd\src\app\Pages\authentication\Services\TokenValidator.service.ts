import { Injectable } from '@angular/core';
import { TokenPayload } from '../Models';

@Injectable({
  providedIn: 'root'
})
export class TokenValidatorService {

  /**
   * Decodes a JWT token safely
   */
  decodeToken(token: string): TokenPayload | null {
    if (!token || typeof token !== 'string') {
      return null;
    }

    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        console.warn('Invalid token format');
        return null;
      }

      const base64Url = parts[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      
      // Add padding if necessary
      const paddedBase64 = base64 + '='.repeat((4 - base64.length % 4) % 4);
      
      const jsonPayload = decodeURIComponent(
        atob(paddedBase64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );

      const decoded = JSON.parse(jsonPayload);
      return this.validateTokenPayload(decoded) ? decoded : null;
    } catch (error) {
      console.error('Token decoding failed:', error);
      return null;
    }
  }

  /**
   * Validates the token payload structure
   */
  private validateTokenPayload(payload: any): payload is TokenPayload {
    return payload &&
           typeof payload === 'object' &&
           typeof payload.exp === 'number' &&
           payload.exp > 0;
  }

  /**
   * Checks if a token is expired
   */
  isTokenExpired(token: string | null): boolean {
    if (!token) {
      return true;
    }

    const decodedToken = this.decodeToken(token);
    if (!decodedToken || !decodedToken.exp) {
      return true;
    }

    const expirationTime = decodedToken.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const bufferTime = 5 * 60 * 1000; // 5 minutes buffer

    return currentTime >= (expirationTime - bufferTime);
  }

  /**
   * Checks if a token will expire soon (within the next 10 minutes)
   */
  isTokenExpiringSoon(token: string | null): boolean {
    if (!token) {
      return false;
    }

    const decodedToken = this.decodeToken(token);
    if (!decodedToken || !decodedToken.exp) {
      return false;
    }

    const expirationTime = decodedToken.exp * 1000;
    const currentTime = Date.now();
    const warningTime = 10 * 60 * 1000; // 10 minutes

    return (expirationTime - currentTime) <= warningTime && (expirationTime - currentTime) > 0;
  }

  /**
   * Gets the remaining time until token expiration
   */
  getTokenRemainingTime(token: string | null): number {
    if (!token) {
      return 0;
    }

    const decodedToken = this.decodeToken(token);
    if (!decodedToken || !decodedToken.exp) {
      return 0;
    }

    const expirationTime = decodedToken.exp * 1000;
    const currentTime = Date.now();
    
    return Math.max(0, expirationTime - currentTime);
  }

  /**
   * Validates token format without decoding
   */
  isValidTokenFormat(token: string): boolean {
    if (!token || typeof token !== 'string') {
      return false;
    }

    const parts = token.split('.');
    return parts.length === 3 && 
           parts.every(part => part.length > 0);
  }

  /**
   * Extracts user roles from token
   */
  getUserRoles(token: string): string[] {
    const decodedToken = this.decodeToken(token);
    if (!decodedToken) {
      return [];
    }

    // Handle both single role and array of roles
    if (Array.isArray(decodedToken.Roles)) {
      return decodedToken.Roles;
    } else if (typeof decodedToken.Roles === 'string') {
      return [decodedToken.Roles];
    }

    return [];
  }

  /**
   * Checks if user has a specific role
   */
  hasRole(token: string, role: string): boolean {
    const roles = this.getUserRoles(token);
    return roles.includes(role);
  }

  /**
   * Gets user ID from token
   */
  getUserId(token: string): string | null {
    const decodedToken = this.decodeToken(token);
    return decodedToken?.id || decodedToken?.nameid || null;
  }

  /**
   * Gets user email from token
   */
  getUserEmail(token: string): string | null {
    const decodedToken = this.decodeToken(token);
    return decodedToken?.Email || decodedToken?.email || null;
  }
}
