import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '../authentication/Guards/auth.guard';
import { UserListComponent } from './Pages/user-list/user-list.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'list',
    pathMatch: 'full',
  },
  {
    path: 'list',
    component: UserListComponent,
    title: 'User Management - Chattrix',
    canActivate: [AuthGuard],
  },
  // Future routes for user details, add/edit user, etc.
  // {
  //   path: 'add',
  //   component: AddEditUserComponent,
  //   title: 'Add User - Chattrix',
  //   canActivate: [AuthGuard],
  // },
  // {
  //   path: 'edit/:id',
  //   component: AddEditUserComponent,
  //   title: 'Edit User - Chattrix',
  //   canActivate: [AuthGuard],
  // },
  // {
  //   path: 'details/:id',
  //   component: UserDetailsComponent,
  //   title: 'User Details - Chattrix',
  //   canActivate: [AuthGuard],
  // },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UserManagementRoutingModule {}
