import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

// Material UI Modules
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';

import { AuthenticationRoutingModule } from './authentication-routing.module';
import { LoginComponent } from './Pages/login/login.component';
import { SignUpComponent } from './Pages/sign-up/sign-up.component';
import { OtpVerificationComponent } from './Pages/otp-verification/otp-verification.component';
import { ResetPasswordComponent } from './Pages/reset-password/reset-password.component';
import { VerifyResetTokenComponent } from './Pages/verify-reset-token/verify-reset-token.component';
import { ProfilePictureComponent } from './Pages/profile-picture/profile-picture.component';
import { ForgetPasswordComponent } from './Pages/forget-password/forget-password.component';

@NgModule({
  declarations: [
    LoginComponent,
    SignUpComponent,
    OtpVerificationComponent,
    ResetPasswordComponent,
    ForgetPasswordComponent,
    VerifyResetTokenComponent,
    ProfilePictureComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    AuthenticationRoutingModule,
    // Material UI Modules
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDividerModule,
  ],
})
export class AuthenticationModule {}
