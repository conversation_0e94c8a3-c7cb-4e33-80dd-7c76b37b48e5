import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    redirectTo: '/auth/login',
    pathMatch: 'full',
  },
  {
    path: 'auth',
    loadChildren: () =>
      import('./Pages/authentication/authentication.module').then(
        (m) => m.AuthenticationModule,
      ),
  },
  {
    path: 'dashboard',
    loadChildren: () =>
      import('./Pages/chattrix/chattrix.module').then((m) => m.ChattrixModule),
  },
  {
    path: 'user-management',
    loadChildren: () =>
      import('./Pages/user-management/user-management.module').then(
        (m) => m.UserManagementModule,
      ),
  },
  {
    path: '**',
    redirectTo: '/auth/login',
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
