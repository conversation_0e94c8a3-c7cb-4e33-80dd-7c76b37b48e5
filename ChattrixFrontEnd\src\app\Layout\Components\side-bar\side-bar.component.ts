import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, Observable, combineLatest } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';

import {
  UserProfileService,
  UserProfile,
} from '../../../Pages/chattrix/Services/UserProfile.service';
import {
  ThemeService,
  ThemeMode,
} from '../../../Pages/chattrix/Services/Theme.service';
import { AuthenticationService } from '../../../Pages/authentication/Services/Authentication.service';

interface NavigationItem {
  label: string;
  icon: string;
  route?: string;
  action?: () => void;
  requiresAdmin?: boolean;
  badge?: number;
}

@Component({
  selector: 'app-side-bar',
  standalone: false,
  templateUrl: './side-bar.component.html',
  styleUrl: './side-bar.component.scss',
})
export class SideBarComponent implements OnInit, On<PERSON><PERSON>roy {
  private destroy$ = new Subject<void>();

  // User profile data
  userProfile$: Observable<UserProfile | null>;
  hasAdminAccess$: Observable<boolean>;
  currentTheme$: Observable<ThemeMode>;
  userRoles: string[] = [];

  // Navigation items
  navigationItems: NavigationItem[] = [
    {
      label: 'Messages',
      icon: 'message',
      route: '/dashboard',
      badge: 0, // Will be updated with actual message count
    },
    {
      label: 'User Management',
      icon: 'people',
      route: '/user-management',
      requiresAdmin: true,
    },
  ];

  constructor(
    private userProfileService: UserProfileService,
    private themeService: ThemeService,
    private authService: AuthenticationService,
    public router: Router,
  ) {
    this.userProfile$ = this.userProfileService.userProfile$;
    this.hasAdminAccess$ = this.userProfileService.hasAdminAccess();
    this.currentTheme$ = this.themeService.currentTheme$;
  }

  ngOnInit(): void {
    // Initialize theme service to listen for system changes
    this.themeService.listenToSystemThemeChanges();

    // Subscribe to user roles for role-based navigation
    this.userProfileService
      .getUserRoles()
      .pipe(takeUntil(this.destroy$))
      .subscribe((roles) => {
        this.userRoles = roles;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Navigate to a specific route
   */
  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme(): void {
    this.themeService.toggleTheme();
  }

  /**
   * Get the current theme mode
   */
  get isDarkMode(): boolean {
    return this.themeService.isDarkMode;
  }

  /**
   * Handle logout
   */
  onLogout(): void {
    this.authService.logout();
  }

  /**
   * Get user initials for avatar fallback
   */
  getUserInitials(profile: UserProfile | null): string {
    return profile?.initials || 'U';
  }

  /**
   * Get user display name
   */
  getUserDisplayName(profile: UserProfile | null): string {
    return profile?.displayName || 'User';
  }

  /**
   * Get user role display
   */
  getUserRole(profile: UserProfile | null): string {
    if (!profile) return 'User';

    if (Array.isArray(profile.role)) {
      return profile.role.join(', ');
    }
    return profile.role || 'User';
  }

  /**
   * Check if navigation item should be visible
   * Simplified approach: hide User Management only for "user" role
   */
  shouldShowNavItem(item: NavigationItem, hasAdminAccess: boolean): boolean {
    if (item.requiresAdmin) {
      // Check if user has only "user" role (case-insensitive)
      const hasOnlyUserRole =
        this.userRoles.length === 1 &&
        this.userRoles[0].toLowerCase() === 'user';

      // Hide User Management for users with only "user" role
      if (hasOnlyUserRole) {
        return false;
      }

      // Show for admin and super admin roles
      const hasAdminRole = this.userRoles.some(
        (role) =>
          role.toLowerCase() === 'admin' ||
          role.toLowerCase() === 'super admin' ||
          role.toLowerCase() === 'superadmin',
      );

      return hasAdminRole;
    }
    return true;
  }

  /**
   * Handle navigation item click
   */
  onNavItemClick(item: NavigationItem): void {
    if (item.action) {
      item.action();
    } else if (item.route) {
      this.navigateTo(item.route);
    }
  }
}
