/* Dashboard Component Styles */

.dashboard-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--bg-primary);
  padding: var(--spacing-lg);
}

.content-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.placeholder-content {
  text-align: center;
  color: var(--text-secondary);
  max-width: 400px;
}

.placeholder-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.placeholder-content h2 {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-md) 0;
}

.placeholder-content p {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.5;
  margin: var(--spacing-sm) 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-content {
    padding: var(--spacing-md);
  }

  .placeholder-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
  }

  .placeholder-content h2 {
    font-size: 1.25rem;
  }

  .placeholder-content p {
    font-size: 0.9rem;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .dashboard-content {
    background: #ffffff;
  }

  .placeholder-content h2 {
    color: #333333;
  }

  .placeholder-content p {
    color: #666666;
  }

  .placeholder-icon {
    color: #999999;
  }
}
