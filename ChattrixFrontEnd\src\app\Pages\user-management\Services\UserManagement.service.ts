import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { catchError, tap, map } from 'rxjs/operators';
import { environment } from '../../../../Environments/environment';
import {
  UserDetails,
  PaginationParameters,
  PagedResponse,
  ApiResponse,
  AddUserRequest,
  UpdateUserRequest,
  DeleteUserRequest,
  UserManagementResponse,
  UsersPagedResponse,
  LoadingStates,
  ErrorStates,
} from '../Models/UserManagement';

@Injectable({
  providedIn: 'root',
})
export class UserManagementService {
  private readonly API_URL = `${environment.apiUrl}/api/Account`;

  // State management
  private _loadingStates$ = new BehaviorSubject<LoadingStates>({
    fetchingUsers: false,
    deletingUser: false,
    updatingUser: false,
    addingUser: false,
  });

  private _errorStates$ = new BehaviorSubject<ErrorStates>({
    fetchError: null,
    deleteError: null,
    updateError: null,
    addError: null,
  });

  private _users$ = new BehaviorSubject<PagedResponse<UserDetails> | null>(null);

  // Public observables
  public readonly loadingStates$ = this._loadingStates$.asObservable();
  public readonly errorStates$ = this._errorStates$.asObservable();
  public readonly users$ = this._users$.asObservable();

  constructor(private httpClient: HttpClient) {}

  /**
   * Get paginated users with filtering and sorting
   */
  getUsers(parameters: PaginationParameters): Observable<PagedResponse<UserDetails>> {
    this.setLoadingState('fetchingUsers', true);
    this.clearError('fetchError');

    let params = new HttpParams()
      .set('pageNumber', parameters.pageNumber.toString())
      .set('pageSize', parameters.pageSize.toString());

    if (parameters.sortField) {
      params = params.set('sortField', parameters.sortField);
    }
    if (parameters.sortOrder) {
      params = params.set('sortOrder', parameters.sortOrder);
    }
    if (parameters.name) {
      params = params.set('name', parameters.name);
    }
    if (parameters.role) {
      params = params.set('role', parameters.role);
    }
    if (parameters.status) {
      params = params.set('status', parameters.status);
    }

    return this.httpClient
      .get<UsersPagedResponse>(`${this.API_URL}/GetUsers`, { params })
      .pipe(
        map((response) => {
          if (response.isSuccess && response.data) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to fetch users');
          }
        }),
        tap((pagedResponse) => {
          this._users$.next(pagedResponse);
        }),
        catchError((error) => {
          const errorMessage = this.extractErrorMessage(error);
          this.setError('fetchError', errorMessage);
          return throwError(() => new Error(errorMessage));
        }),
        tap(() => {
          this.setLoadingState('fetchingUsers', false);
        })
      );
  }

  /**
   * Get user by ID
   */
  getUserById(userId: string): Observable<UserDetails> {
    return this.httpClient
      .get<UserManagementResponse>(`${this.API_URL}/GetUserById/${userId}`)
      .pipe(
        map((response) => {
          if (response.isSuccess && response.data) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to fetch user');
          }
        }),
        catchError((error) => {
          const errorMessage = this.extractErrorMessage(error);
          return throwError(() => new Error(errorMessage));
        })
      );
  }

  /**
   * Add new user
   */
  addUser(userData: AddUserRequest): Observable<ApiResponse> {
    this.setLoadingState('addingUser', true);
    this.clearError('addError');

    const formData = this.createFormData(userData);

    return this.httpClient
      .post<ApiResponse>(`${this.API_URL}/AddUser`, formData)
      .pipe(
        tap((response) => {
          if (!response.isSuccess) {
            throw new Error(response.message || 'Failed to add user');
          }
        }),
        catchError((error) => {
          const errorMessage = this.extractErrorMessage(error);
          this.setError('addError', errorMessage);
          return throwError(() => new Error(errorMessage));
        }),
        tap(() => {
          this.setLoadingState('addingUser', false);
        })
      );
  }

  /**
   * Update user
   */
  updateUser(userId: string, userData: UpdateUserRequest): Observable<ApiResponse> {
    this.setLoadingState('updatingUser', true);
    this.clearError('updateError');

    return this.httpClient
      .put<ApiResponse>(`${this.API_URL}/UpdateUser/${userId}`, userData)
      .pipe(
        tap((response) => {
          if (!response.isSuccess) {
            throw new Error(response.message || 'Failed to update user');
          }
        }),
        catchError((error) => {
          const errorMessage = this.extractErrorMessage(error);
          this.setError('updateError', errorMessage);
          return throwError(() => new Error(errorMessage));
        }),
        tap(() => {
          this.setLoadingState('updatingUser', false);
        })
      );
  }

  /**
   * Delete user
   */
  deleteUser(userId: string, deleteRequest: DeleteUserRequest): Observable<ApiResponse> {
    this.setLoadingState('deletingUser', true);
    this.clearError('deleteError');

    return this.httpClient
      .delete<ApiResponse>(`${this.API_URL}/DeleteUser/${userId}`, {
        body: deleteRequest,
      })
      .pipe(
        tap((response) => {
          if (!response.isSuccess) {
            throw new Error(response.message || 'Failed to delete user');
          }
        }),
        catchError((error) => {
          const errorMessage = this.extractErrorMessage(error);
          this.setError('deleteError', errorMessage);
          return throwError(() => new Error(errorMessage));
        }),
        tap(() => {
          this.setLoadingState('deletingUser', false);
        })
      );
  }

  /**
   * Get all users (for dropdowns, etc.)
   */
  getAllUsers(): Observable<UserDetails[]> {
    return this.httpClient
      .get<ApiResponse<UserDetails[]>>(`${this.API_URL}/GetAll`)
      .pipe(
        map((response) => {
          if (response.isSuccess && response.data) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to fetch all users');
          }
        }),
        catchError((error) => {
          const errorMessage = this.extractErrorMessage(error);
          return throwError(() => new Error(errorMessage));
        })
      );
  }

  /**
   * Create FormData for file uploads
   */
  private createFormData(userData: AddUserRequest): FormData {
    const formData = new FormData();
    
    formData.append('email', userData.email);
    formData.append('fullName', userData.fullName);
    formData.append('password', userData.password);
    formData.append('isActive', userData.isActive.toString());
    
    if (userData.phoneNumber) {
      formData.append('phoneNumber', userData.phoneNumber);
    }
    if (userData.description) {
      formData.append('description', userData.description);
    }
    if (userData.profileImage) {
      formData.append('profileImage', userData.profileImage);
    }

    return formData;
  }

  /**
   * Set loading state for specific operation
   */
  private setLoadingState(operation: keyof LoadingStates, loading: boolean): void {
    const currentStates = this._loadingStates$.value;
    this._loadingStates$.next({
      ...currentStates,
      [operation]: loading,
    });
  }

  /**
   * Set error for specific operation
   */
  private setError(operation: keyof ErrorStates, error: string | null): void {
    const currentStates = this._errorStates$.value;
    this._errorStates$.next({
      ...currentStates,
      [operation]: error,
    });
  }

  /**
   * Clear error for specific operation
   */
  private clearError(operation: keyof ErrorStates): void {
    this.setError(operation, null);
  }

  /**
   * Extract error message from HTTP error response
   */
  private extractErrorMessage(error: any): string {
    if (error?.error?.message) {
      return error.error.message;
    }
    if (error?.message) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return 'An unexpected error occurred';
  }

  /**
   * Clear all states (useful for component cleanup)
   */
  clearStates(): void {
    this._loadingStates$.next({
      fetchingUsers: false,
      deletingUser: false,
      updatingUser: false,
      addingUser: false,
    });
    this._errorStates$.next({
      fetchError: null,
      deleteError: null,
      updateError: null,
      addError: null,
    });
    this._users$.next(null);
  }
}
