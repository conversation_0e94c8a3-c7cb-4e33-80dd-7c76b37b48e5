/* User Details Modal */
.user-details-modal {
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.modal-title {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.close-button {
  color: var(--text-secondary);
  
  &:hover {
    color: var(--text-primary);
    background: var(--bg-hover);
  }
}

/* Modal Content */
.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
}

/* Profile Section */
.profile-section {
  margin-bottom: var(--spacing-lg);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.profile-picture {
  flex-shrink: 0;
}

.profile-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--border-primary);
}

.profile-placeholder {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: var(--accent-green);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--border-primary);
}

.initials {
  color: white;
  font-size: 2rem;
  font-weight: 600;
}

.basic-info {
  flex: 1;
}

.user-name {
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.user-email {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin: 0 0 var(--spacing-md) 0;
}

.status-badge {
  display: flex;
  align-items: center;
}

.status-active {
  background: #e8f5e8;
  color: #2e7d32;
  
  mat-icon {
    color: #2e7d32;
    font-size: 18px;
    width: 18px;
    height: 18px;
    margin-right: var(--spacing-xs);
  }
}

.status-inactive {
  background: #ffebee;
  color: #d32f2f;
  
  mat-icon {
    color: #d32f2f;
    font-size: 18px;
    width: 18px;
    height: 18px;
    margin-right: var(--spacing-xs);
  }
}

/* Details Grid */
.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.info-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);

  .mat-mdc-card-header {
    padding: var(--spacing-md) var(--spacing-md) 0;
  }

  .mat-mdc-card-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;

    mat-icon {
      color: var(--accent-green);
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }

  .mat-mdc-card-content {
    padding: var(--spacing-md);
  }
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-secondary);

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.9rem;
}

.info-value {
  color: var(--text-primary);
  font-weight: 500;
  text-align: right;
  word-break: break-word;
}

/* Roles */
.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.role-admin {
  background: #e3f2fd;
  color: #1976d2;
}

.role-super-admin {
  background: #fce4ec;
  color: #c2185b;
}

.role-user {
  background: #f3e5f5;
  color: #7b1fa2;
}

.role-default {
  background: #f5f5f5;
  color: #666666;
}

/* Description */
.description-text {
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  white-space: pre-wrap;
}

/* Modal Actions */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.cancel-button {
  color: var(--text-secondary);
  
  &:hover {
    background: var(--bg-hover);
  }
}

.edit-button {
  mat-icon {
    margin-right: var(--spacing-xs);
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-details-modal {
    max-width: 95vw;
    max-height: 95vh;
  }

  .modal-header,
  .modal-content,
  .modal-actions {
    padding: var(--spacing-md);
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .profile-image,
  .profile-placeholder {
    width: 80px;
    height: 80px;
  }

  .initials {
    font-size: 1.5rem;
  }

  .user-name {
    font-size: 1.5rem;
  }

  .user-email {
    font-size: 1rem;
  }

  .details-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .info-value {
    text-align: left;
  }

  .modal-actions {
    flex-direction: column-reverse;
    gap: var(--spacing-sm);
  }

  .cancel-button,
  .edit-button {
    width: 100%;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .modal-header,
  .modal-actions {
    background: #f5f5f5;
    border-color: #e0e0e0;
  }

  .modal-content {
    background: #ffffff;
  }

  .modal-title {
    color: #333333;
  }

  .profile-header,
  .info-card {
    background: #ffffff;
    border-color: #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .user-name {
    color: #333333;
  }

  .user-email {
    color: #666666;
  }

  .info-label {
    color: #666666;
  }

  .info-value {
    color: #333333;
  }

  .description-text {
    color: #333333;
  }

  .info-row {
    border-bottom-color: #f0f0f0;
  }
}
