<div class="auth-container">
  <mat-card class="auth-card">
    <!-- Header Section -->
    <div class="auth-header">
      <div class="auth-logo">
        <img
          src="logo/logo2.png"
          alt="Chattrix Logo"
          (error)="onImageError($event)"
          [style.display]="logoLoaded ? 'block' : 'none'"
        />
        <div
          class="logo-fallback"
          [style.display]="logoLoaded ? 'none' : 'flex'"
        >
          <span class="logo-text">C</span>
        </div>
      </div>
      <h1 class="auth-title">Forgot Password?</h1>
      <p class="auth-subtitle">
        Enter your email address and we'll send you a verification code to reset
        your password.
      </p>
    </div>

    <!-- Forgot Password Form -->
    <form
      [formGroup]="forgotPasswordForm"
      (ngSubmit)="onSendResetLink()"
      class="auth-form"
    >
      <!-- Email Field -->
      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Email Address</mat-label>
        <input
          matInput
          type="email"
          formControlName="email"
          placeholder="Enter your email address"
          autocomplete="email"
        />
        <mat-icon matSuffix>email</mat-icon>
        <mat-error *ngIf="emailControl?.hasError('required')">
          Email is required
        </mat-error>
        <mat-error *ngIf="emailControl?.hasError('email')">
          Please enter a valid email address
        </mat-error>
      </mat-form-field>

      <!-- Send Reset Link Button -->
      <button
        mat-raised-button
        color="primary"
        type="submit"
        class="reset-button full-width"
        [disabled]="forgotPasswordForm.invalid || isLoading"
      >
        <div class="button-content">
          <mat-spinner
            *ngIf="isLoading"
            diameter="20"
            class="button-spinner"
          ></mat-spinner>
          <span [class.hidden]="isLoading">Send Reset Code</span>
        </div>
      </button>
    </form>

    <!-- Footer Section -->
    <div class="auth-footer">
      <mat-divider></mat-divider>
      <div class="back-to-login">
        <button
          mat-button
          color="primary"
          (click)="onBackToLogin()"
          class="back-button"
        >
          <mat-icon>arrow_back</mat-icon>
          Back to Login
        </button>
      </div>
    </div>
  </mat-card>
</div>
