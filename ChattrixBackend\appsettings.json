{"ConnectionStrings": {"DefaultConnection": "Server=RAUF-0504\\SQLEXPRESS;Database=ChatApp;Trusted_Connection=True;TrustServerCertificate=True;MultipleActiveResultSets=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "EmailConfiguration": {"From": "<EMAIL>", "SmtpServer": "smtp.gmail.com", "Port": 465, "Username": "<EMAIL>", "Password": "ewglhjmlawcqsnwh"}, "JWTSetting": {"securityKey": "ThisIsASuperSecretKey1234567890987654321", "ValidAudience": "http://localhost:4200", "ValidIssuer": "https://localhost:5020"}, "AWS": {"Profile": "default", "Region": "eu-north-1", "S3": {"BucketName": "fullstacksfl", "AccessKey": "********************", "SecretKey": "YD07GzzO4iAVtxDw6j5ql3901P/gCupOwaC1fy+n"}}, "AllowedHosts": "*"}