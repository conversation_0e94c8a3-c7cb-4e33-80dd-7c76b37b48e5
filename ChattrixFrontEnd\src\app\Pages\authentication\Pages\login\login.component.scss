/* Login Component Specific Styles */

.full-width {
  width: 100%;
  margin-bottom: var(--spacing-md);
}

.forgot-password-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: var(--spacing-md);
}

.forgot-password-link {
  font-size: var(--font-size-sm);
  color: var(--accent-green) !important;
  text-transform: none;
  padding: 0;
  min-width: auto;

  &:hover {
    color: var(--accent-green-hover) !important;
    background: transparent;
  }
}

.login-button {
  height: 44px;
  font-size: var(--font-size-base);
  font-weight: 500;
  text-transform: none;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  position: relative;

  &:disabled {
    opacity: 0.6;
  }

  .button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    position: relative;
  }

  .hidden {
    visibility: hidden;
    opacity: 0;
  }
}

.button-spinner {
  position: absolute;

  transform: translate(-50%, -50%);
}

.signup-prompt {
  margin: var(--spacing-lg) 0 0;
  text-align: center;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.signup-link {
  color: var(--accent-green) !important;
  text-transform: none;
  font-weight: 500;
  padding: 0;
  min-width: auto;
  margin-left: var(--spacing-xs);

  &:hover {
    color: var(--accent-green-hover) !important;
    background: transparent;
  }
}

/* Material UI Form Field Customizations */
::ng-deep .mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    background-color: var(--bg-input);
    border-radius: var(--radius-md);
  }

  .mat-mdc-form-field-label {
    color: var(--text-secondary);
  }

  .mat-mdc-form-field-input {
    color: var(--text-primary);
  }

  .mat-mdc-form-field-icon-suffix {
    color: var(--text-secondary);
  }

  &.mat-focused {
    .mat-mdc-form-field-label {
      color: var(--accent-green);
    }
  }
}

/* Snackbar Styles */
::ng-deep .error-snackbar {
  background-color: var(--error) !important;
  color: var(--text-primary) !important;
}

::ng-deep .success-snackbar {
  background-color: var(--success) !important;
  color: var(--text-primary) !important;
}

/* Responsive Design */
@media (max-width: 480px) {
  .auth-card {
    margin: var(--spacing-sm);
    max-width: calc(100vw - 2rem);
  }

  .auth-header {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .auth-form {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .auth-footer {
    padding: var(--spacing-md);
  }

  .auth-logo {
    width: 60px;
    height: 60px;
  }

  .auth-title {
    font-size: var(--font-size-xl);
  }
}

/* Focus and Hover States */
.mat-mdc-button:focus {
  outline: 2px solid var(--accent-green);
  outline-offset: 2px;
}

.mat-mdc-form-field:focus-within {
  .mat-mdc-text-field-wrapper {
    border: 2px solid var(--accent-green);
  }
}

/* Loading State Animation */
.button-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Logo Fallback Styles */
.logo-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--accent-green) 0%,
    var(--accent-green-light) 100%
  );
  border-radius: 50%;
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
